<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('temp_pre_orders', function (Blueprint $table) {
            // Fix tax column size to accommodate larger values
            // Change from smaller decimal to DECIMAL(12,2) to handle values up to 9999999999.99
            $table->decimal('tax', 12, 2)->default(0.00)->change();
            $table->decimal('total_tax', 12, 2)->default(0.00)->change();

            // Also fix related amount columns that might have similar issues
            $table->decimal('amount', 12, 2)->default(0.00)->change();
            $table->decimal('total_amt', 12, 2)->default(0.00)->change();
            $table->decimal('product_price', 12, 2)->nullable()->change();
            $table->decimal('delivery_charges', 12, 2)->default(0.00)->change();
            $table->decimal('service_charges', 12, 2)->default(0.00)->change();
            $table->decimal('total_delivery_charges', 12, 2)->default(0.00)->change();
            $table->decimal('line_delivery_charges', 12, 2)->default(0.00)->change();
            $table->decimal('applied_discount', 12, 2)->default(0.00)->change();
            $table->decimal('total_applied_discount', 12, 2)->default(0.00)->change();
            $table->decimal('total_third_party_charges', 12, 2)->default(0.00)->change();
            $table->decimal('tp_delivery_charges', 12, 2)->nullable()->change();
            $table->decimal('tp_aggregator_charges', 12, 2)->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('temp_pre_orders', function (Blueprint $table) {
            // Revert back to original smaller decimal sizes (if needed)
            // Note: This might cause data loss if there are large values
            $table->decimal('tax', 10, 2)->default(0.00)->change();
            $table->decimal('total_tax', 10, 2)->default(0.00)->change();
            $table->decimal('amount', 10, 2)->default(0.00)->change();
            $table->decimal('total_amt', 10, 2)->default(0.00)->change();
            $table->decimal('product_price', 10, 2)->nullable()->change();
            $table->decimal('delivery_charges', 10, 2)->default(0.00)->change();
            $table->decimal('service_charges', 10, 2)->default(0.00)->change();
            $table->decimal('total_delivery_charges', 10, 2)->default(0.00)->change();
            $table->decimal('line_delivery_charges', 10, 2)->default(0.00)->change();
            $table->decimal('applied_discount', 10, 2)->default(0.00)->change();
            $table->decimal('total_applied_discount', 10, 2)->default(0.00)->change();
            $table->decimal('total_third_party_charges', 10, 2)->default(0.00)->change();
            $table->decimal('tp_delivery_charges', 10, 2)->nullable()->change();
            $table->decimal('tp_aggregator_charges', 10, 2)->nullable()->change();
        });
    }
};
